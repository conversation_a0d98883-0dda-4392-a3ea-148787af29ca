import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

import '../../feature/auth/presentation/screens/phone_otp_screen.dart';
import '../../feature/auth/presentation/screens/phone_registration_screen.dart';
import '../../feature/home/<USER>/sdm_home.dart';
import '../../feature/profile/view/profile_screen.dart';
import '../../feature/splash/view/splash_screen.dart';

part 'app_router.gr.dart';

/// Auto Route configuration for type-safe navigation
@AutoRouterConfig()
class AppRouter extends RootStackRouter {
  @override
  List<AutoRoute> get routes => [
        // Splash Route (initial)
        AutoRoute(
          page: SplashRoute.page,
          path: '/',
          initial: true,
        ),

        // Authentication Routes
        AutoRoute(
          page: PhoneRegistrationRoute.page,
          path: '/phone-registration',
        ),
        AutoRoute(
          page: PhoneOtpRoute.page,
          path: '/phone-otp',
        ),

        // Main App Routes
        AutoRoute(
          page: HomeRoute.page,
          path: '/home',
        ),
        AutoRoute(
          page: ProfileRoute.page,
          path: '/profile',
        ),
      ];
}

/// Phone Registration Page
@RoutePage()
class PhoneRegistrationPage extends StatelessWidget {
  const PhoneRegistrationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const PhoneRegistrationScreen();
  }
}

/// Phone OTP Page
@RoutePage()
class PhoneOtpPage extends StatelessWidget {
  const PhoneOtpPage({
    super.key,
    this.verificationId,
  });

  final String? verificationId;

  @override
  Widget build(BuildContext context) {
    return PhoneOtpScreen(verificationId: verificationId);
  }
}

/// Splash Page
@RoutePage()
class SplashPage extends StatelessWidget {
  const SplashPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const SplashScreen();
  }
}

/// Home Page
@RoutePage()
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const SdmHome();
  }
}

/// Profile Page
@RoutePage()
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const ProfileScreen();
  }
}
