import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

import '../../feature/auth/presentation/screens/phone_otp_screen.dart';
import '../../feature/auth/presentation/screens/phone_registration_screen.dart';
import '../../feature/home/<USER>/sdm_home.dart';

part 'app_router.gr.dart';

/// Auto Route configuration for type-safe navigation
@AutoRouterConfig()
class AppRouter extends RootStackRouter {
  @override
  List<AutoRoute> get routes => [
        // Authentication Routes Only
        AutoRoute(
          page: PhoneRegistrationRoute.page,
          path: '/auth/phone-registration',
          initial: true,
        ),
        AutoRoute(
          page: PhoneOtpRoute.page,
          path: '/auth/phone-otp',
        ),

        // Home route (not used since we use old navigation)
        AutoRoute(
          page: HomeRoute.page,
          path: '/home',
        ),

        // Fallback to auth
        AutoRoute(
          page: PhoneRegistrationRoute.page,
          path: '*',
        ),
      ];
}

/// Phone Registration Page
@RoutePage()
class PhoneRegistrationPage extends StatelessWidget {
  const PhoneRegistrationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const PhoneRegistrationScreen();
  }
}

/// Phone OTP Page
@RoutePage()
class PhoneOtpPage extends StatelessWidget {
  const PhoneOtpPage({
    super.key,
    this.verificationId,
  });

  final String? verificationId;

  @override
  Widget build(BuildContext context) {
    return PhoneOtpScreen(verificationId: verificationId);
  }
}

/// Home Page
@RoutePage()
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const SdmHome();
  }
}
