// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

part of 'app_router.dart';

/// generated route for
/// [AboutUsPage]
class AboutUsRoute extends PageRouteInfo<void> {
  const AboutUsRoute({List<PageRouteInfo>? children})
      : super(AboutUsRoute.name, initialChildren: children);

  static const String name = 'AboutUsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const AboutUsPage();
    },
  );
}

/// generated route for
/// [AdminHomePage]
class AdminHomeRoute extends PageRouteInfo<void> {
  const AdminHomeRoute({List<PageRouteInfo>? children})
      : super(AdminHomeRoute.name, initialChildren: children);

  static const String name = 'AdminHomeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const AdminHomePage();
    },
  );
}

/// generated route for
/// [BlogViewPage]
class BlogViewRoute extends PageRouteInfo<BlogViewRouteArgs> {
  BlogViewRoute({
    Key? key,
    required BlogModel blogModel,
    List<PageRouteInfo>? children,
  }) : super(
          BlogViewRoute.name,
          args: BlogViewRouteArgs(key: key, blogModel: blogModel),
          initialChildren: children,
        );

  static const String name = 'BlogViewRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<BlogViewRouteArgs>();
      return BlogViewPage(key: args.key, blogModel: args.blogModel);
    },
  );
}

class BlogViewRouteArgs {
  const BlogViewRouteArgs({this.key, required this.blogModel});

  final Key? key;

  final BlogModel blogModel;

  @override
  String toString() {
    return 'BlogViewRouteArgs{key: $key, blogModel: $blogModel}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! BlogViewRouteArgs) return false;
    return key == other.key && blogModel == other.blogModel;
  }

  @override
  int get hashCode => key.hashCode ^ blogModel.hashCode;
}

/// generated route for
/// [BlogsPage]
class BlogsRoute extends PageRouteInfo<void> {
  const BlogsRoute({List<PageRouteInfo>? children})
      : super(BlogsRoute.name, initialChildren: children);

  static const String name = 'BlogsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const BlogsPage();
    },
  );
}

/// generated route for
/// [CalendarEventsPage]
class CalendarEventsRoute extends PageRouteInfo<void> {
  const CalendarEventsRoute({List<PageRouteInfo>? children})
      : super(CalendarEventsRoute.name, initialChildren: children);

  static const String name = 'CalendarEventsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const CalendarEventsPage();
    },
  );
}

/// generated route for
/// [CertificateDetailsPage]
class CertificateDetailsRoute
    extends PageRouteInfo<CertificateDetailsRouteArgs> {
  CertificateDetailsRoute({
    Key? key,
    required Credential credential,
    List<PageRouteInfo>? children,
  }) : super(
          CertificateDetailsRoute.name,
          args: CertificateDetailsRouteArgs(key: key, credential: credential),
          initialChildren: children,
        );

  static const String name = 'CertificateDetailsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CertificateDetailsRouteArgs>();
      return CertificateDetailsPage(key: args.key, credential: args.credential);
    },
  );
}

class CertificateDetailsRouteArgs {
  const CertificateDetailsRouteArgs({this.key, required this.credential});

  final Key? key;

  final Credential credential;

  @override
  String toString() {
    return 'CertificateDetailsRouteArgs{key: $key, credential: $credential}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CertificateDetailsRouteArgs) return false;
    return key == other.key && credential == other.credential;
  }

  @override
  int get hashCode => key.hashCode ^ credential.hashCode;
}

/// generated route for
/// [CertificatesPage]
class CertificatesRoute extends PageRouteInfo<CertificatesRouteArgs> {
  CertificatesRoute({
    Key? key,
    AccredibleCredentialsCertificates? certificates,
    List<PageRouteInfo>? children,
  }) : super(
          CertificatesRoute.name,
          args: CertificatesRouteArgs(key: key, certificates: certificates),
          initialChildren: children,
        );

  static const String name = 'CertificatesRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CertificatesRouteArgs>(
        orElse: () => const CertificatesRouteArgs(),
      );
      return CertificatesPage(key: args.key, certificates: args.certificates);
    },
  );
}

class CertificatesRouteArgs {
  const CertificatesRouteArgs({this.key, this.certificates});

  final Key? key;

  final AccredibleCredentialsCertificates? certificates;

  @override
  String toString() {
    return 'CertificatesRouteArgs{key: $key, certificates: $certificates}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CertificatesRouteArgs) return false;
    return key == other.key && certificates == other.certificates;
  }

  @override
  int get hashCode => key.hashCode ^ certificates.hashCode;
}

/// generated route for
/// [ContactUsPage]
class ContactUsRoute extends PageRouteInfo<void> {
  const ContactUsRoute({List<PageRouteInfo>? children})
      : super(ContactUsRoute.name, initialChildren: children);

  static const String name = 'ContactUsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ContactUsPage();
    },
  );
}

/// generated route for
/// [EnterEmailPage]
class EnterEmailRoute extends PageRouteInfo<void> {
  const EnterEmailRoute({List<PageRouteInfo>? children})
      : super(EnterEmailRoute.name, initialChildren: children);

  static const String name = 'EnterEmailRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const EnterEmailPage();
    },
  );
}

/// generated route for
/// [HomePage]
class HomeRoute extends PageRouteInfo<void> {
  const HomeRoute({List<PageRouteInfo>? children})
      : super(HomeRoute.name, initialChildren: children);

  static const String name = 'HomeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const HomePage();
    },
  );
}

/// generated route for
/// [MusicListPage]
class MusicListRoute extends PageRouteInfo<void> {
  const MusicListRoute({List<PageRouteInfo>? children})
      : super(MusicListRoute.name, initialChildren: children);

  static const String name = 'MusicListRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const MusicListPage();
    },
  );
}

/// generated route for
/// [MusicPlayerPage]
class MusicPlayerRoute extends PageRouteInfo<void> {
  const MusicPlayerRoute({List<PageRouteInfo>? children})
      : super(MusicPlayerRoute.name, initialChildren: children);

  static const String name = 'MusicPlayerRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const MusicPlayerPage();
    },
  );
}

/// generated route for
/// [PhoneOtpPage]
class PhoneOtpRoute extends PageRouteInfo<PhoneOtpRouteArgs> {
  PhoneOtpRoute({
    Key? key,
    String? verificationId,
    List<PageRouteInfo>? children,
  }) : super(
          PhoneOtpRoute.name,
          args: PhoneOtpRouteArgs(key: key, verificationId: verificationId),
          initialChildren: children,
        );

  static const String name = 'PhoneOtpRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<PhoneOtpRouteArgs>(
        orElse: () => const PhoneOtpRouteArgs(),
      );
      return PhoneOtpPage(key: args.key, verificationId: args.verificationId);
    },
  );
}

class PhoneOtpRouteArgs {
  const PhoneOtpRouteArgs({this.key, this.verificationId});

  final Key? key;

  final String? verificationId;

  @override
  String toString() {
    return 'PhoneOtpRouteArgs{key: $key, verificationId: $verificationId}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! PhoneOtpRouteArgs) return false;
    return key == other.key && verificationId == other.verificationId;
  }

  @override
  int get hashCode => key.hashCode ^ verificationId.hashCode;
}

/// generated route for
/// [PhoneRegistrationPage]
class PhoneRegistrationRoute extends PageRouteInfo<void> {
  const PhoneRegistrationRoute({List<PageRouteInfo>? children})
      : super(PhoneRegistrationRoute.name, initialChildren: children);

  static const String name = 'PhoneRegistrationRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const PhoneRegistrationPage();
    },
  );
}

/// generated route for
/// [ProfilePage]
class ProfileRoute extends PageRouteInfo<void> {
  const ProfileRoute({List<PageRouteInfo>? children})
      : super(ProfileRoute.name, initialChildren: children);

  static const String name = 'ProfileRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ProfilePage();
    },
  );
}

/// generated route for
/// [PublicationInfoPage]
class PublicationInfoRoute extends PageRouteInfo<PublicationInfoRouteArgs> {
  PublicationInfoRoute({
    Key? key,
    required PublicationModel publicationModel,
    List<PageRouteInfo>? children,
  }) : super(
          PublicationInfoRoute.name,
          args: PublicationInfoRouteArgs(
            key: key,
            publicationModel: publicationModel,
          ),
          initialChildren: children,
        );

  static const String name = 'PublicationInfoRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<PublicationInfoRouteArgs>();
      return PublicationInfoPage(
        key: args.key,
        publicationModel: args.publicationModel,
      );
    },
  );
}

class PublicationInfoRouteArgs {
  const PublicationInfoRouteArgs({this.key, required this.publicationModel});

  final Key? key;

  final PublicationModel publicationModel;

  @override
  String toString() {
    return 'PublicationInfoRouteArgs{key: $key, publicationModel: $publicationModel}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! PublicationInfoRouteArgs) return false;
    return key == other.key && publicationModel == other.publicationModel;
  }

  @override
  int get hashCode => key.hashCode ^ publicationModel.hashCode;
}

/// generated route for
/// [PublicationsPage]
class PublicationsRoute extends PageRouteInfo<void> {
  const PublicationsRoute({List<PageRouteInfo>? children})
      : super(PublicationsRoute.name, initialChildren: children);

  static const String name = 'PublicationsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const PublicationsPage();
    },
  );
}

/// generated route for
/// [SplashPage]
class SplashRoute extends PageRouteInfo<void> {
  const SplashRoute({List<PageRouteInfo>? children})
      : super(SplashRoute.name, initialChildren: children);

  static const String name = 'SplashRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const SplashPage();
    },
  );
}

/// generated route for
/// [VideosPage]
class VideosRoute extends PageRouteInfo<void> {
  const VideosRoute({List<PageRouteInfo>? children})
      : super(VideosRoute.name, initialChildren: children);

  static const String name = 'VideosRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const VideosPage();
    },
  );
}

/// generated route for
/// [YoutubeVideoPlayerPage]
class YoutubeVideoPlayerRoute
    extends PageRouteInfo<YoutubeVideoPlayerRouteArgs> {
  YoutubeVideoPlayerRoute({
    Key? key,
    required VideoModel videoModel,
    List<PageRouteInfo>? children,
  }) : super(
          YoutubeVideoPlayerRoute.name,
          args: YoutubeVideoPlayerRouteArgs(key: key, videoModel: videoModel),
          initialChildren: children,
        );

  static const String name = 'YoutubeVideoPlayerRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<YoutubeVideoPlayerRouteArgs>();
      return YoutubeVideoPlayerPage(key: args.key, videoModel: args.videoModel);
    },
  );
}

class YoutubeVideoPlayerRouteArgs {
  const YoutubeVideoPlayerRouteArgs({this.key, required this.videoModel});

  final Key? key;

  final VideoModel videoModel;

  @override
  String toString() {
    return 'YoutubeVideoPlayerRouteArgs{key: $key, videoModel: $videoModel}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! YoutubeVideoPlayerRouteArgs) return false;
    return key == other.key && videoModel == other.videoModel;
  }

  @override
  int get hashCode => key.hashCode ^ videoModel.hashCode;
}
