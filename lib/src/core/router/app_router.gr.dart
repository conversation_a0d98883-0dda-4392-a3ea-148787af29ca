// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

part of 'app_router.dart';

/// generated route for
/// [AboutUsPage]
class AboutUsRoute extends PageRouteInfo<void> {
  const AboutUsRoute({List<PageRouteInfo>? children})
      : super(AboutUsRoute.name, initialChildren: children);

  static const String name = 'AboutUsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const AboutUsPage();
    },
  );
}

/// generated route for
/// [BlogViewPage]
class BlogViewRoute extends PageRouteInfo<BlogViewRouteArgs> {
  BlogViewRoute({
    Key? key,
    required BlogModel blogModel,
    List<PageRouteInfo>? children,
  }) : super(
          BlogViewRoute.name,
          args: BlogViewRouteArgs(key: key, blogModel: blogModel),
          initialChildren: children,
        );

  static const String name = 'BlogViewRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<BlogViewRouteArgs>();
      return BlogViewPage(key: args.key, blogModel: args.blogModel);
    },
  );
}

class BlogViewRouteArgs {
  const BlogViewRouteArgs({this.key, required this.blogModel});

  final Key? key;

  final BlogModel blogModel;

  @override
  String toString() {
    return 'BlogViewRouteArgs{key: $key, blogModel: $blogModel}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! BlogViewRouteArgs) return false;
    return key == other.key && blogModel == other.blogModel;
  }

  @override
  int get hashCode => key.hashCode ^ blogModel.hashCode;
}

/// generated route for
/// [BlogsPage]
class BlogsRoute extends PageRouteInfo<void> {
  const BlogsRoute({List<PageRouteInfo>? children})
      : super(BlogsRoute.name, initialChildren: children);

  static const String name = 'BlogsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const BlogsPage();
    },
  );
}

/// generated route for
/// [CalendarEventsPage]
class CalendarEventsRoute extends PageRouteInfo<void> {
  const CalendarEventsRoute({List<PageRouteInfo>? children})
      : super(CalendarEventsRoute.name, initialChildren: children);

  static const String name = 'CalendarEventsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const CalendarEventsPage();
    },
  );
}

/// generated route for
/// [ContactUsPage]
class ContactUsRoute extends PageRouteInfo<void> {
  const ContactUsRoute({List<PageRouteInfo>? children})
      : super(ContactUsRoute.name, initialChildren: children);

  static const String name = 'ContactUsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ContactUsPage();
    },
  );
}

/// generated route for
/// [HomePage]
class HomeRoute extends PageRouteInfo<void> {
  const HomeRoute({List<PageRouteInfo>? children})
      : super(HomeRoute.name, initialChildren: children);

  static const String name = 'HomeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const HomePage();
    },
  );
}

/// generated route for
/// [PhoneOtpPage]
class PhoneOtpRoute extends PageRouteInfo<PhoneOtpRouteArgs> {
  PhoneOtpRoute({
    Key? key,
    String? verificationId,
    List<PageRouteInfo>? children,
  }) : super(
          PhoneOtpRoute.name,
          args: PhoneOtpRouteArgs(key: key, verificationId: verificationId),
          initialChildren: children,
        );

  static const String name = 'PhoneOtpRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<PhoneOtpRouteArgs>(
        orElse: () => const PhoneOtpRouteArgs(),
      );
      return PhoneOtpPage(key: args.key, verificationId: args.verificationId);
    },
  );
}

class PhoneOtpRouteArgs {
  const PhoneOtpRouteArgs({this.key, this.verificationId});

  final Key? key;

  final String? verificationId;

  @override
  String toString() {
    return 'PhoneOtpRouteArgs{key: $key, verificationId: $verificationId}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! PhoneOtpRouteArgs) return false;
    return key == other.key && verificationId == other.verificationId;
  }

  @override
  int get hashCode => key.hashCode ^ verificationId.hashCode;
}

/// generated route for
/// [PhoneRegistrationPage]
class PhoneRegistrationRoute extends PageRouteInfo<void> {
  const PhoneRegistrationRoute({List<PageRouteInfo>? children})
      : super(PhoneRegistrationRoute.name, initialChildren: children);

  static const String name = 'PhoneRegistrationRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const PhoneRegistrationPage();
    },
  );
}

/// generated route for
/// [ProfilePage]
class ProfileRoute extends PageRouteInfo<void> {
  const ProfileRoute({List<PageRouteInfo>? children})
      : super(ProfileRoute.name, initialChildren: children);

  static const String name = 'ProfileRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ProfilePage();
    },
  );
}

/// generated route for
/// [PublicationsPage]
class PublicationsRoute extends PageRouteInfo<void> {
  const PublicationsRoute({List<PageRouteInfo>? children})
      : super(PublicationsRoute.name, initialChildren: children);

  static const String name = 'PublicationsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const PublicationsPage();
    },
  );
}

/// generated route for
/// [SplashPage]
class SplashRoute extends PageRouteInfo<void> {
  const SplashRoute({List<PageRouteInfo>? children})
      : super(SplashRoute.name, initialChildren: children);

  static const String name = 'SplashRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const SplashPage();
    },
  );
}

/// generated route for
/// [VideosPage]
class VideosRoute extends PageRouteInfo<void> {
  const VideosRoute({List<PageRouteInfo>? children})
      : super(VideosRoute.name, initialChildren: children);

  static const String name = 'VideosRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const VideosPage();
    },
  );
}
