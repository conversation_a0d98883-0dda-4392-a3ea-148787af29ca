import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:shridattmandir/src/core/core.dart';
import 'package:shridattmandir/src/core/router/app_router.dart';
import 'package:shridattmandir/src/feature/feature.dart';
import 'package:shridattmandir/src/feature/home/<USER>/profile_completion_card.dart';
import 'package:shridattmandir/src/shared/shared.dart'
    show SdmLaunchUrl, SdmNetworkImage, SdmPrimaryCta, SdmShimmerLoader;
import 'package:url_launcher/url_launcher.dart';

class SdmHome extends StatefulWidget {
  const SdmHome({
    super.key,
  });

  @override
  State<SdmHome> createState() => _SdmHomeState();
}

/// Helper function to navigate using auto_route
void _navigateToRoute(
    BuildContext context, String routeName, Object? arguments) {
  switch (routeName) {
    case SdmRouter.profile:
      context.router.push(const ProfileRoute());
      break;
    case SdmRouter.aboutUs:
      context.router.push(const AboutUsRoute());
      break;
    case SdmRouter.blogs:
      context.router.push(const BlogsRoute());
      break;
    case SdmRouter.blogView:
      if (arguments is BlogModel) {
        context.router.push(BlogViewRoute(blogModel: arguments));
      }
      break;
    case SdmRouter.calendarEvents:
      context.router.push(const CalendarEventsRoute());
      break;
    case SdmRouter.contactUs:
      context.router.push(const ContactUsRoute());
      break;
    case SdmRouter.publications:
      context.router.push(const PublicationsRoute());
      break;
    case SdmRouter.publicationInfo:
      if (arguments is PublicationModel) {
        context.router.push(PublicationInfoRoute(publicationModel: arguments));
      }
      break;
    case SdmRouter.videos:
      context.router.push(const VideosRoute());
      break;
    case SdmRouter.videoPlayer:
      if (arguments is VideoModel) {
        context.router.push(YoutubeVideoPlayerRoute(videoModel: arguments));
      }
      break;
    case SdmRouter.musicList:
      context.router.push(const MusicListRoute());
      break;
    case SdmRouter.musicPlayer:
      context.router.push(const MusicPlayerRoute());
      break;
    case SdmRouter.enterEmail:
      context.router.push(const EnterEmailRoute());
      break;
    case SdmRouter.certificates:
      if (arguments is AccredibleCredentialsCertificates) {
        context.router.push(CertificatesRoute(certificates: arguments));
      } else {
        context.router.push(CertificatesRoute());
      }
      break;
    case SdmRouter.certificateDetails:
      if (arguments is Credential) {
        context.router.push(CertificateDetailsRoute(credential: arguments));
      }
      break;
    case SdmRouter.adminHome:
      context.router.push(const AdminHomeRoute());
      break;
    default:
      // For routes not yet migrated, show a message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text('Route $routeName not yet migrated to auto_route')),
      );
  }
}

class _SdmHomeState extends State<SdmHome> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback(
      (_) async {
        final analyticsCubit = context.read<AnalyticsCubit>();
        final deeplinkCubit = context.read<DeeplinkCubit>();
        final purchasesCubit = context.read<PurchasesCubit>();

        await Future.wait(
          [
            deeplinkCubit.initialize(),
            analyticsCubit.onTrackAnalyticsEvent(
              HomeLoadEvent(),
            ),
            analyticsCubit.onSetUserId(),
            purchasesCubit.setAttributes(),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => HomeCubit()
        ..fetchHomeQuotes()
        ..fetchCalendarEvents()
        ..fetchVideos()
        ..inAppUpdate()
        ..inAppReview(),
      child: BlocListener<DeeplinkCubit, DeeplinkState>(
        listenWhen: (previous, current) =>
            previous.routeName != current.routeName ||
            previous.messageOnForeground != current.messageOnForeground,
        listener: (context, state) async {
          final deeplinkCubit = context.read<DeeplinkCubit>();
          if (state.messageOnForeground && state.remoteMessage != null) {
            showOverlayNotification(
              (context) {
                return NotificationOverlay(
                  message: state.remoteMessage!,
                  onNotificationTap: () async {
                    OverlaySupportEntry.of(context)?.dismiss();
                    deeplinkCubit.reset();
                    await deeplinkCubit
                        .handleNotification(state.remoteMessage!);
                  },
                );
              },
              duration: Duration.zero,
            );

            return;
          }

          if (state.routeName == null) {
            return;
          }

          if (state.routeName == SdmRouter.paywall) {
            await PaywallManager.showPaywallModal(context: context);
          } else {
            _navigateToRoute(context, state.routeName!, state.arguments);
          }

          deeplinkCubit.reset();
        },
        child: Builder(
          builder: (context) {
            context.watch<PurchasesCubit>().state;
            final homeState = context.watch<HomeCubit>().state;
            final homeImages =
                context.watch<RemoteConfigCubit>().state.homeImages;
            final homeImageUrl =
                homeImages.length >= 3 ? homeImages[2] : SdmUrls.kMountainImage;

            return FlavorBanner(
              child: Scaffold(
                bottomNavigationBar: const MusicNowPlayingBottomBar(),
                extendBodyBehindAppBar: true,
                appBar: AppBar(
                  automaticallyImplyLeading: false,
                  leading: Builder(
                    builder: (context) => IconButton(
                      iconSize: 22.w,
                      icon: Icon(
                        Icons.menu,
                        color: SdmPalette.white,
                        size: 22.w,
                      ),
                      onPressed: () => Scaffold.of(context).openDrawer(),
                    ),
                  ),
                  actions: [
                    Hero(
                      tag: 'logo',
                      child: Assets.sdmImages.logo.image(
                        height: 30.h,
                        width: 30.h,
                      ),
                    ),
                    SizedBox(
                      width: 8.w,
                    ),
                  ],
                ),
                drawer: const AppDrawer(),
                body: ListView(
                  physics: const ClampingScrollPhysics(),
                  padding: const EdgeInsets.only(top: 0),
                  children: <Widget>[
                    Stack(
                      children: <Widget>[
                        SizedBox(
                          width: 1.sw,
                          height: 0.36.sh,
                          //dynamic random image home with shimmer effect
                          child: homeState.loading
                              ? SdmShimmerLoader(
                                  child: Container(
                                    color: SdmPalette.white,
                                  ),
                                )
                              : SdmNetworkImage(
                                  url: homeImageUrl,
                                  fit: BoxFit.cover,
                                  placeHolderWidget: const ColoredBox(
                                    color: SdmPalette.primary,
                                  ),
                                ),
                        ),
                        Quotes(
                          changeQuote: homeState.homeQuote?.text ?? '',
                        ),
                        homeState.loading
                            ? const SizedBox()
                            : Positioned(
                                bottom: 6.h,
                                right: 6.w,
                                child: SdmPrimaryCta(
                                  backgroundColor: SdmPalette.black54,
                                  onPressed: () {
                                    SdmLaunchUrl.sdmLaunchUrl(
                                      SdmUrls.kSelfTestQuiz,
                                      mode: LaunchMode.inAppWebView,
                                    );
                                  },
                                  child: Padding(
                                    padding: const EdgeInsets.all(2.0).w,
                                    child: Text(
                                      'SELF TEST',
                                      style: TextStyle(
                                        color: SdmPalette.white,
                                        fontSize: 12.sp,
                                      ),
                                    ),
                                  ),
                                ),
                              )
                      ],
                    ),
                    if (!homeState.loading)
                      BlocBuilder<UserSessionCubit, UserSessionState>(
                        builder: (context, userSessionState) {
                          if (userSessionState.userProfile?.name != null ||
                              userSessionState.userProfile?.email != null) {
                            return const SizedBox();
                          }
                          return const ProfileCompletionCard();
                        },
                      ),
                    (homeState.loading)
                        ? const SdmShimmerLoader(
                            child: UpcomingEventsSkeleton(),
                          )
                        : const UpcomingEvents(),
                    (homeState.loading)
                        ? const SdmShimmerLoader(
                            child: LatestVideosSkeleton(),
                          )
                        : const LatestVideos(),
                    if (!homeState.loading) ...[
                      Center(
                        child: Text(
                          '''


      OUR GREAT HERITAGE

      Hansa, Brahma, Atri, Datta,
      Balbhima, Bhagirathi, Datta,
      Madhuri bows Shri Datta,
      Let us bow Madhuri Datta.
      ''',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14.sp,
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      Center(
                        child: TextButton(
                          style: TextButton.styleFrom(
                            padding: EdgeInsets.zero,
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          ),
                          onPressed: () {
                            Navigator.pushNamed(
                              context,
                              SdmRouter.aboutUs,
                            );
                          },
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'About Us',
                                style: TextStyle(
                                  color: SdmPalette.primary,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14.sp,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const Icon(
                                Icons.keyboard_arrow_down,
                                color: SdmPalette.primary,
                              )
                            ],
                          ),
                        ),
                      ),
                    ],
                    SizedBox(
                      height: 10.h,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
